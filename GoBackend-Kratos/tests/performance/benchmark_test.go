package performance

import (
	"context"
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gobackend-hvac-kratos/internal/langgraph"
	"gobackend-hvac-kratos/internal/executive"
	"gobackend-hvac-kratos/internal/data"
)

// 🚀 Performance Benchmarks for GoBackend-Kratos

// Benchmark LangGraph Workflow Service
func BenchmarkWorkflowService_ExecuteWorkflow(b *testing.B) {
	service := setupBenchmarkWorkflowService(b)
	ctx := context.Background()
	
	input := map[string]interface{}{
		"customer_data": map[string]interface{}{
			"name":         "<PERSON>",
			"email":        "<EMAIL>",
			"service_type": "heating",
		},
	}

	b.ResetTimer()
	b.<PERSON>(func(pb *testing.PB) {
		for pb.Next() {
			_, err := service.ExecuteWorkflow(ctx, "customer_onboarding", input)
			if err != nil {
				b.<PERSON><PERSON><PERSON>("Workflow execution failed: %v", err)
			}
		}
	})
}

func BenchmarkWorkflowService_MapToJSON(b *testing.B) {
	service := setupBenchmarkWorkflowService(b)
	
	testData := map[string]interface{}{
		"customer_id":   "123",
		"priority":      "high",
		"service_type":  "emergency",
		"location":      "123 Main St",
		"description":   "Heating system not working",
		"timestamp":     time.Now(),
		"metadata": map[string]interface{}{
			"source":     "web",
			"user_agent": "Mozilla/5.0",
			"ip_address": "***********",
		},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		result := service.MapToJSON(testData)
		if result == "" {
			b.Error("MapToJSON returned empty string")
		}
	}
}

func BenchmarkWorkflowService_JSONToMap(b *testing.B) {
	service := setupBenchmarkWorkflowService(b)
	
	jsonStr := `{
		"customer_id": "123",
		"priority": "high",
		"service_type": "emergency",
		"location": "123 Main St",
		"description": "Heating system not working",
		"metadata": {
			"source": "web",
			"user_agent": "Mozilla/5.0",
			"ip_address": "***********"
		}
	}`

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := service.JSONToMap(jsonStr)
		if err != nil {
			b.Errorf("JSONToMap failed: %v", err)
		}
	}
}

// Benchmark Executive Service
func BenchmarkExecutiveService_ProcessIncomingEmail(b *testing.B) {
	service := setupBenchmarkExecutiveService(b)
	ctx := context.Background()

	// Setup test email in database
	testEmail := &data.Email{
		ID:      1,
		From:    "<EMAIL>",
		To:      []string{"<EMAIL>"},
		Subject: "HVAC Service Request",
		Body:    "My heating system is not working properly. Please schedule a service call.",
	}

	// Insert test email (mock implementation)
	insertTestEmail(b, testEmail)

	request := &executive.EmailProcessingRequest{
		EmailID:           1,
		ForceReprocessing: false,
		SkipWorkflows:     false,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		result, err := service.ProcessIncomingEmail(ctx, request)
		if err != nil {
			b.Errorf("Email processing failed: %v", err)
		}
		if !result.Success {
			b.Error("Email processing was not successful")
		}
	}
}

func BenchmarkTriageEngine_TriageEmail(b *testing.B) {
	triageEngine := setupBenchmarkTriageEngine(b)
	ctx := context.Background()

	testEmail := &data.Email{
		ID:      1,
		From:    "<EMAIL>",
		Subject: "Urgent: Heating System Failure",
		Body:    "My heating system stopped working this morning. The house is getting very cold. Please help immediately!",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		result, err := triageEngine.TriageEmail(ctx, testEmail)
		if err != nil {
			b.Errorf("Email triage failed: %v", err)
		}
		if result.Confidence <= 0 {
			b.Error("Triage confidence should be positive")
		}
	}
}

func BenchmarkDraftEngine_GenerateResponse(b *testing.B) {
	draftEngine := setupBenchmarkDraftEngine(b)
	ctx := context.Background()

	testEmail := &data.Email{
		ID:      1,
		From:    "<EMAIL>",
		Subject: "Request for HVAC Quote",
		Body:    "I need a quote for installing a new HVAC system in my 2000 sq ft home.",
	}

	triageResult := &executive.TriageResult{
		Action:           data.TriageActionRespond,
		Category:         "quote_request",
		Priority:         data.PriorityNormal,
		HVACServiceType:  "installation",
		CustomerType:     "residential",
		DetectedIntent:   "request_quote",
		Sentiment:        data.SentimentPositive,
		Confidence:       0.9,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		result, err := draftEngine.GenerateResponse(ctx, testEmail, triageResult)
		if err != nil {
			b.Errorf("Response generation failed: %v", err)
		}
		if result.Subject == "" || result.Body == "" {
			b.Error("Generated response should have subject and body")
		}
	}
}

// Concurrent Processing Benchmarks
func BenchmarkConcurrentWorkflowExecution(b *testing.B) {
	service := setupBenchmarkWorkflowService(b)
	ctx := context.Background()

	input := map[string]interface{}{
		"customer_data": map[string]interface{}{
			"name":         "John Doe",
			"email":        "<EMAIL>",
			"service_type": "heating",
		},
	}

	concurrencyLevels := []int{1, 5, 10, 20, 50}

	for _, concurrency := range concurrencyLevels {
		b.Run(fmt.Sprintf("Concurrency_%d", concurrency), func(b *testing.B) {
			b.SetParallelism(concurrency)
			b.ResetTimer()
			
			b.RunParallel(func(pb *testing.PB) {
				for pb.Next() {
					_, err := service.ExecuteWorkflow(ctx, "customer_onboarding", input)
					if err != nil {
						b.Errorf("Workflow execution failed: %v", err)
					}
				}
			})
		})
	}
}

func BenchmarkConcurrentEmailProcessing(b *testing.B) {
	service := setupBenchmarkExecutiveService(b)
	ctx := context.Background()

	// Setup multiple test emails
	setupMultipleTestEmails(b, 100)

	concurrencyLevels := []int{1, 5, 10, 20}

	for _, concurrency := range concurrencyLevels {
		b.Run(fmt.Sprintf("Concurrency_%d", concurrency), func(b *testing.B) {
			b.SetParallelism(concurrency)
			b.ResetTimer()
			
			var emailCounter int64
			b.RunParallel(func(pb *testing.PB) {
				for pb.Next() {
					emailID := (emailCounter % 100) + 1
					emailCounter++
					
					request := &executive.EmailProcessingRequest{
						EmailID:           emailID,
						ForceReprocessing: false,
						SkipWorkflows:     true, // Skip workflows for performance testing
					}

					_, err := service.ProcessIncomingEmail(ctx, request)
					if err != nil {
						b.Errorf("Email processing failed: %v", err)
					}
				}
			})
		})
	}
}

// Memory Usage Benchmarks
func BenchmarkMemoryUsage_WorkflowExecution(b *testing.B) {
	service := setupBenchmarkWorkflowService(b)
	ctx := context.Background()

	input := map[string]interface{}{
		"customer_data": map[string]interface{}{
			"name":         "John Doe",
			"email":        "<EMAIL>",
			"service_type": "heating",
		},
	}

	b.ReportAllocs()
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		_, err := service.ExecuteWorkflow(ctx, "customer_onboarding", input)
		if err != nil {
			b.Errorf("Workflow execution failed: %v", err)
		}
	}
}

func BenchmarkMemoryUsage_EmailTriage(b *testing.B) {
	triageEngine := setupBenchmarkTriageEngine(b)
	ctx := context.Background()

	testEmail := &data.Email{
		ID:      1,
		From:    "<EMAIL>",
		Subject: "Service Request",
		Body:    "I need HVAC service for my heating system.",
	}

	b.ReportAllocs()
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		_, err := triageEngine.TriageEmail(ctx, testEmail)
		if err != nil {
			b.Errorf("Email triage failed: %v", err)
		}
	}
}

// Load Testing
func TestLoadTesting_WorkflowService(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping load test in short mode")
	}

	service := setupBenchmarkWorkflowService(t)
	ctx := context.Background()

	input := map[string]interface{}{
		"customer_data": map[string]interface{}{
			"name":         "Load Test Customer",
			"email":        "<EMAIL>",
			"service_type": "heating",
		},
	}

	// Test parameters
	duration := 30 * time.Second
	concurrency := 10
	expectedRPS := 100 // requests per second

	var wg sync.WaitGroup
	var successCount, errorCount int64
	var mu sync.Mutex

	startTime := time.Now()
	endTime := startTime.Add(duration)

	// Start concurrent workers
	for i := 0; i < concurrency; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			
			for time.Now().Before(endTime) {
				_, err := service.ExecuteWorkflow(ctx, "customer_onboarding", input)
				
				mu.Lock()
				if err != nil {
					errorCount++
				} else {
					successCount++
				}
				mu.Unlock()

				// Small delay to control request rate
				time.Sleep(time.Millisecond * 10)
			}
		}()
	}

	wg.Wait()
	actualDuration := time.Since(startTime)

	// Calculate metrics
	totalRequests := successCount + errorCount
	actualRPS := float64(totalRequests) / actualDuration.Seconds()
	errorRate := float64(errorCount) / float64(totalRequests) * 100

	t.Logf("Load Test Results:")
	t.Logf("Duration: %v", actualDuration)
	t.Logf("Total Requests: %d", totalRequests)
	t.Logf("Successful Requests: %d", successCount)
	t.Logf("Failed Requests: %d", errorCount)
	t.Logf("Actual RPS: %.2f", actualRPS)
	t.Logf("Error Rate: %.2f%%", errorRate)

	// Assertions
	assert.Greater(t, actualRPS, float64(expectedRPS)*0.8, "RPS should be at least 80% of expected")
	assert.Less(t, errorRate, 5.0, "Error rate should be less than 5%")
	assert.Greater(t, successCount, int64(0), "Should have successful requests")
}

// Helper functions for benchmark setup
func setupBenchmarkWorkflowService(tb testing.TB) *langgraph.WorkflowService {
	// Mock implementation for benchmarking
	// In real implementation, this would setup actual service with test database
	return &langgraph.WorkflowService{
		// Mock service for benchmarking
	}
}

func setupBenchmarkExecutiveService(tb testing.TB) *executive.Service {
	// Mock implementation for benchmarking
	return &executive.Service{
		// Mock service for benchmarking
	}
}

func setupBenchmarkTriageEngine(tb testing.TB) *executive.TriageEngine {
	// Mock implementation for benchmarking
	return &executive.TriageEngine{
		// Mock triage engine for benchmarking
	}
}

func setupBenchmarkDraftEngine(tb testing.TB) *executive.DraftEngine {
	// Mock implementation for benchmarking
	return &executive.DraftEngine{
		// Mock draft engine for benchmarking
	}
}

func insertTestEmail(tb testing.TB, email *data.Email) {
	// Mock implementation - in real test this would insert into test database
}

func setupMultipleTestEmails(tb testing.TB, count int) {
	// Mock implementation - in real test this would setup multiple test emails
	for i := 1; i <= count; i++ {
		email := &data.Email{
			ID:      int64(i),
			From:    fmt.Sprintf("<EMAIL>", i),
			Subject: fmt.Sprintf("Service Request #%d", i),
			Body:    fmt.Sprintf("This is test email #%d for performance testing", i),
		}
		insertTestEmail(tb, email)
	}
}

// Performance Requirements Validation
func TestPerformanceRequirements(t *testing.T) {
	t.Run("Workflow_Execution_Time", func(t *testing.T) {
		service := setupBenchmarkWorkflowService(t)
		ctx := context.Background()

		input := map[string]interface{}{
			"customer_data": map[string]interface{}{
				"name":  "Performance Test",
				"email": "<EMAIL>",
			},
		}

		start := time.Now()
		_, err := service.ExecuteWorkflow(ctx, "customer_onboarding", input)
		duration := time.Since(start)

		require.NoError(t, err)
		assert.Less(t, duration, 5*time.Second, "Workflow execution should complete within 5 seconds")
	})

	t.Run("Email_Processing_Time", func(t *testing.T) {
		service := setupBenchmarkExecutiveService(t)
		ctx := context.Background()

		// Setup test email
		testEmail := &data.Email{
			ID:      1,
			From:    "<EMAIL>",
			Subject: "Performance Test Email",
			Body:    "This is a performance test email for measuring processing time.",
		}
		insertTestEmail(t, testEmail)

		request := &executive.EmailProcessingRequest{
			EmailID: 1,
		}

		start := time.Now()
		result, err := service.ProcessIncomingEmail(ctx, request)
		duration := time.Since(start)

		require.NoError(t, err)
		require.NotNil(t, result)
		assert.Less(t, duration, 3*time.Second, "Email processing should complete within 3 seconds")
	})

	t.Run("Memory_Usage_Limits", func(t *testing.T) {
		// This test would measure memory usage and ensure it stays within limits
		// Implementation would depend on specific memory profiling tools
		t.Skip("Memory usage testing requires specific profiling setup")
	})
}
