package unit

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/mock"
	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"

	"gobackend-hvac-kratos/internal/executive"
	"gobackend-hvac-kratos/internal/data"
	"gobackend-hvac-kratos/internal/ai"
	"gobackend-hvac-kratos/internal/email"
	"gobackend-hvac-kratos/internal/langchain"
)

// 🧪 Executive AI Assistant Unit Tests

// Mock services for testing
type MockAIService struct {
	mock.Mock
}

func (m *MockAIService) SmartEmailProcessing(ctx context.Context, content, sender string) (*langchain.EmailAnalysisResult, error) {
	args := m.Called(ctx, content, sender)
	return args.Get(0).(*langchain.EmailAnalysisResult), args.Error(1)
}

type MockEmailService struct {
	mock.Mock
}

func (m *MockEmailService) ProcessEmail(ctx context.Context, emailID int64) error {
	args := m.Called(ctx, emailID)
	return args.Error(0)
}

type MockLangChainService struct {
	mock.Mock
}

func (m *MockLangChainService) ProcessEmail(ctx context.Context, content, sender string) (*langchain.EmailAnalysisResult, error) {
	args := m.Called(ctx, content, sender)
	return args.Get(0).(*langchain.EmailAnalysisResult), args.Error(1)
}

func (m *MockLangChainService) AnalyzeContent(ctx context.Context, content, analysisType string) (*langchain.AnalysisResult, error) {
	args := m.Called(ctx, content, analysisType)
	return args.Get(0).(*langchain.AnalysisResult), args.Error(1)
}

// Test Executive Service
func TestExecutiveService_ProcessIncomingEmail(t *testing.T) {
	// Setup mocks
	mockAI := &MockAIService{}
	mockEmail := &MockEmailService{}
	mockLangChain := &MockLangChainService{}
	
	// Setup test database
	db := setupTestDB(t)
	
	// Setup test email
	testEmail := &data.Email{
		ID:      1,
		From:    "<EMAIL>",
		To:      []string{"<EMAIL>"},
		Subject: "Urgent: Heating System Not Working",
		Body:    "My heating system stopped working this morning. Please help!",
	}
	
	// Insert test email into database
	err := db.Create(testEmail).Error
	require.NoError(t, err)

	// Setup expected AI analysis result
	expectedAnalysis := &langchain.EmailAnalysisResult{
		EmailID: "email-1",
		Classification: langchain.EmailClassification{
			Category:  langchain.EmailCategoryServiceRequest,
			Priority:  langchain.PriorityHigh,
			Sentiment: langchain.SentimentNeutral,
			Language:  "en",
			IsSpam:    false,
			IsUrgent:  true,
		},
		ContentAnalysis: langchain.EmailContentAnalysis{
			MainTopic:      "heating system malfunction",
			KeyIssues:      []string{"heating not working", "urgent repair needed"},
			CustomerIntent: "request_emergency_service",
			ActionRequired: "schedule_emergency_technician",
		},
		Confidence: 0.92,
		ProcessedAt: time.Now(),
	}

	// Setup mock expectations
	mockLangChain.On("ProcessEmail", mock.Anything, mock.AnythingOfType("string"), "<EMAIL>").
		Return(expectedAnalysis, nil)

	// Create service
	config := &executive.Config{
		EnableWorkflowAutomation: true,
		EnableMetricsCollection:  true,
		AutoRespondThreshold:     0.8,
		MaxResponseLength:        2000,
		BusinessHoursStart:       "09:00",
		BusinessHoursEnd:         "17:00",
	}

	logger := log.NewStdLogger(nil)
	service, err := executive.NewService(db, mockAI, mockEmail, mockLangChain, config, logger)
	require.NoError(t, err)

	t.Run("Successful_Email_Processing", func(t *testing.T) {
		ctx := context.Background()
		request := &executive.EmailProcessingRequest{
			EmailID:           1,
			ForceReprocessing: false,
			SkipWorkflows:     false,
		}

		result, err := service.ProcessIncomingEmail(ctx, request)
		require.NoError(t, err)
		assert.NotNil(t, result)
		assert.True(t, result.Success)
		assert.Equal(t, int64(1), result.EmailID)
		assert.NotNil(t, result.TriageResult)
		assert.Greater(t, result.ProcessingTime, time.Duration(0))

		// Verify triage result
		assert.Equal(t, data.TriageActionEscalate, result.TriageResult.Action)
		assert.Equal(t, data.PriorityHigh, result.TriageResult.Priority)
		assert.Contains(t, result.TriageResult.Category, "service")
	})

	t.Run("Force_Reprocessing", func(t *testing.T) {
		ctx := context.Background()
		request := &executive.EmailProcessingRequest{
			EmailID:           1,
			ForceReprocessing: true,
			SkipWorkflows:     false,
		}

		result, err := service.ProcessIncomingEmail(ctx, request)
		require.NoError(t, err)
		assert.NotNil(t, result)
		assert.True(t, result.Success)
		
		// Should process even if already processed
		mockLangChain.AssertCalled(t, "ProcessEmail", mock.Anything, mock.AnythingOfType("string"), "<EMAIL>")
	})

	t.Run("Skip_Workflows", func(t *testing.T) {
		ctx := context.Background()
		request := &executive.EmailProcessingRequest{
			EmailID:           1,
			ForceReprocessing: true,
			SkipWorkflows:     true,
		}

		result, err := service.ProcessIncomingEmail(ctx, request)
		require.NoError(t, err)
		assert.NotNil(t, result)
		assert.True(t, result.Success)
		assert.Empty(t, result.WorkflowResults)
	})

	// Verify mock expectations
	mockLangChain.AssertExpectations(t)
}

// Test Triage Engine
func TestTriageEngine_TriageEmail(t *testing.T) {
	mockAI := &MockAIService{}
	mockLangChain := &MockLangChainService{}
	db := setupTestDB(t)
	
	config := &executive.Config{
		AutoRespondThreshold: 0.8,
		BusinessHoursStart:   "09:00",
		BusinessHoursEnd:     "17:00",
	}

	logger := log.NewStdLogger(nil)
	triageEngine := executive.NewTriageEngine(db, mockAI, mockLangChain, config, logger)

	testEmail := &data.Email{
		ID:      1,
		From:    "<EMAIL>",
		Subject: "TERRIBLE SERVICE - DEMAND REFUND",
		Body:    "Your technician was rude and incompetent. I demand a full refund immediately!",
	}

	expectedAnalysis := &langchain.EmailAnalysisResult{
		Classification: langchain.EmailClassification{
			Category:  langchain.EmailCategoryComplaint,
			Priority:  langchain.PriorityHigh,
			Sentiment: langchain.SentimentNegative,
			IsUrgent:  true,
		},
		ContentAnalysis: langchain.EmailContentAnalysis{
			MainTopic:      "service complaint",
			KeyIssues:      []string{"rude technician", "poor service", "refund request"},
			CustomerIntent: "complaint",
			ActionRequired: "escalate_to_manager",
		},
		Confidence: 0.95,
	}

	mockLangChain.On("ProcessEmail", mock.Anything, mock.AnythingOfType("string"), "<EMAIL>").
		Return(expectedAnalysis, nil)

	t.Run("Angry_Customer_Escalation", func(t *testing.T) {
		ctx := context.Background()
		
		result, err := triageEngine.TriageEmail(ctx, testEmail)
		require.NoError(t, err)
		assert.NotNil(t, result)
		
		// Should escalate angry customer emails
		assert.Equal(t, data.TriageActionEscalate, result.Action)
		assert.Equal(t, data.PriorityHigh, result.Priority)
		assert.Equal(t, data.SentimentNegative, result.Sentiment)
		assert.Greater(t, result.Confidence, 0.8)
		assert.Contains(t, result.Reasoning, "angry")
	})

	mockLangChain.AssertExpectations(t)
}

// Test Draft Engine
func TestDraftEngine_GenerateResponse(t *testing.T) {
	mockAI := &MockAIService{}
	mockLangChain := &MockLangChainService{}
	db := setupTestDB(t)
	
	config := &executive.Config{
		MaxResponseLength:  2000,
		BusinessHoursStart: "09:00",
		BusinessHoursEnd:   "17:00",
	}

	logger := log.NewStdLogger(nil)
	draftEngine := executive.NewDraftEngine(db, mockAI, mockLangChain, config, logger)

	testEmail := &data.Email{
		ID:      1,
		From:    "<EMAIL>",
		Subject: "Request for HVAC Quote",
		Body:    "I need a quote for installing a new HVAC system in my 2000 sq ft home.",
	}

	triageResult := &executive.TriageResult{
		Action:           data.TriageActionRespond,
		Category:         "quote_request",
		Priority:         data.PriorityNormal,
		HVACServiceType:  "installation",
		CustomerType:     "residential",
		DetectedIntent:   "request_quote",
		Sentiment:        data.SentimentPositive,
		Confidence:       0.9,
	}

	t.Run("Quote_Request_Response", func(t *testing.T) {
		ctx := context.Background()
		
		result, err := draftEngine.GenerateResponse(ctx, testEmail, triageResult)
		require.NoError(t, err)
		assert.NotNil(t, result)
		
		// Verify response structure
		assert.NotEmpty(t, result.Subject)
		assert.NotEmpty(t, result.Body)
		assert.NotEmpty(t, result.HTMLBody)
		assert.Equal(t, data.ResponseTypeProfessional, result.ResponseType)
		assert.Equal(t, data.ResponseToneProfessional, result.ResponseTone)
		assert.Greater(t, result.QualityScore, 0.7)
		assert.Contains(t, result.ToAddresses, "<EMAIL>")
	})

	t.Run("Template_Variable_Replacement", func(t *testing.T) {
		variables := map[string]string{
			"customer_name":    "John Doe",
			"service_type":     "installation",
			"original_subject": "Request for HVAC Quote",
		}

		template := "Dear {customer_name}, thank you for your {service_type} inquiry regarding '{original_subject}'."
		result := draftEngine.ReplaceTemplateVariables(template, variables)
		
		expected := "Dear John Doe, thank you for your installation inquiry regarding 'Request for HVAC Quote'."
		assert.Equal(t, expected, result)
	})

	t.Run("Customer_Name_Extraction", func(t *testing.T) {
		testCases := []struct {
			email    string
			expected string
		}{
			{"<EMAIL>", "John Doe"},
			{"<EMAIL>", "Jane Smith"},
			{"<EMAIL>", "Bob"},
			{"", "Valued Customer"},
		}

		for _, tc := range testCases {
			result := draftEngine.ExtractCustomerName(tc.email)
			assert.Equal(t, tc.expected, result)
		}
	})
}

// Test Memory Bank Service
func TestMemoryBankService_ProcessEmailForMemory(t *testing.T) {
	mockAI := &MockAIService{}
	db := setupTestDB(t)
	
	config := &executive.Config{}
	logger := log.NewStdLogger(nil)
	memoryBank := executive.NewMemoryBankService(db, mockAI, config, logger)

	testEmail := &data.Email{
		ID:      1,
		From:    "<EMAIL>",
		Subject: "Second Service Call This Month",
		Body:    "This is my second call this month. The heating system is acting up again.",
	}

	triageResult := &executive.TriageResult{
		Category:        "service_request",
		HVACServiceType: "repair",
		CustomerType:    "residential",
		DetectedEntities: map[string]interface{}{
			"equipment": "heating system",
			"frequency": "second call this month",
		},
	}

	t.Run("Memory_Creation_From_Email", func(t *testing.T) {
		ctx := context.Background()
		
		memories, err := memoryBank.ProcessEmailForMemory(ctx, testEmail, triageResult)
		require.NoError(t, err)
		assert.NotNil(t, memories)
		assert.NotEmpty(t, memories)

		// Should create customer memory
		customerMemory := findMemoryByType(memories, "customer")
		assert.NotNil(t, customerMemory)
		assert.Contains(t, customerMemory.Content, "<EMAIL>")

		// Should create service pattern memory
		patternMemory := findMemoryByType(memories, "service_pattern")
		assert.NotNil(t, patternMemory)
		assert.Contains(t, patternMemory.Content, "second call")
	})
}

// Helper functions
func setupTestDB(t *testing.T) *gorm.DB {
	// In a real test, you would use a test database
	// For this example, we'll mock the database operations
	// or use an in-memory database like SQLite
	
	// This is a placeholder - implement actual test DB setup
	return nil
}

func findMemoryByType(memories []*executive.MemoryUpdate, memoryType string) *executive.MemoryUpdate {
	for _, memory := range memories {
		if memory.Type == memoryType {
			return memory
		}
	}
	return nil
}

// Test Configuration Validation
func TestExecutiveConfig_Validation(t *testing.T) {
	t.Run("Valid_Configuration", func(t *testing.T) {
		config := &executive.Config{
			EnableWorkflowAutomation: true,
			EnableMetricsCollection:  true,
			AutoRespondThreshold:     0.8,
			MaxResponseLength:        2000,
			BusinessHoursStart:       "09:00",
			BusinessHoursEnd:         "17:00",
		}

		err := config.Validate()
		assert.NoError(t, err)
	})

	t.Run("Invalid_Threshold", func(t *testing.T) {
		config := &executive.Config{
			AutoRespondThreshold: 1.5, // Invalid: > 1.0
		}

		err := config.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "threshold")
	})

	t.Run("Invalid_Business_Hours", func(t *testing.T) {
		config := &executive.Config{
			BusinessHoursStart: "25:00", // Invalid hour
			BusinessHoursEnd:   "17:00",
		}

		err := config.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "business hours")
	})
}

// Test Error Handling
func TestExecutiveService_ErrorHandling(t *testing.T) {
	mockAI := &MockAIService{}
	mockEmail := &MockEmailService{}
	mockLangChain := &MockLangChainService{}
	db := setupTestDB(t)

	config := &executive.Config{}
	logger := log.NewStdLogger(nil)
	service, err := executive.NewService(db, mockAI, mockEmail, mockLangChain, config, logger)
	require.NoError(t, err)

	t.Run("Nonexistent_Email_Processing", func(t *testing.T) {
		ctx := context.Background()
		request := &executive.EmailProcessingRequest{
			EmailID: 99999, // Nonexistent email
		}

		result, err := service.ProcessIncomingEmail(ctx, request)
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "Failed to retrieve email")
	})

	t.Run("AI_Service_Failure", func(t *testing.T) {
		// Setup mock to return error
		mockLangChain.On("ProcessEmail", mock.Anything, mock.AnythingOfType("string"), mock.AnythingOfType("string")).
			Return(nil, assert.AnError)

		ctx := context.Background()
		testEmail := &data.Email{
			ID:   1,
			From: "<EMAIL>",
			Body: "Test email",
		}

		// Insert test email
		db.Create(testEmail)

		request := &executive.EmailProcessingRequest{
			EmailID: 1,
		}

		result, err := service.ProcessIncomingEmail(ctx, request)
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "Triage failed")
	})
}
