package langgraph

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/log"
)

// 🕸️ LangGraph Workflow Service for HVAC CRM
// Advanced AI workflow orchestration with graph-based execution

type WorkflowService struct {
	log       *log.Helper
	workflows map[string]*HVACWorkflow
	config    *LangGraphConfig
	db        *sql.DB
}

type LangGraphConfig struct {
	MaxExecutionTime time.Duration `yaml:"max_execution_time"`
	MaxRetries       int           `yaml:"max_retries"`
	EnableLogging    bool          `yaml:"enable_logging"`
	CacheResults     bool          `yaml:"cache_results"`
}

// HVACWorkflow represents a complex HVAC business process
type HVACWorkflow struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Nodes       map[string]*WorkflowNode `json:"nodes"`
	Edges       []WorkflowEdge         `json:"edges"`
	StartNode   string                 `json:"start_node"`
	EndNodes    []string               `json:"end_nodes"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// WorkflowNode represents a single step in the workflow
type WorkflowNode struct {
	ID          string                 `json:"id"`
	Type        NodeType               `json:"type"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Handler     NodeHandler            `json:"-"`
	Config      map[string]interface{} `json:"config"`
	Timeout     time.Duration          `json:"timeout"`
}

// WorkflowEdge represents a connection between nodes
type WorkflowEdge struct {
	From      string                 `json:"from"`
	To        string                 `json:"to"`
	Condition string                 `json:"condition,omitempty"`
	Weight    float64                `json:"weight,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// NodeType defines the type of workflow node
type NodeType string

const (
	NodeTypeInput       NodeType = "input"
	NodeTypeAIAnalysis  NodeType = "ai_analysis"
	NodeTypeDecision    NodeType = "decision"
	NodeTypeAction      NodeType = "action"
	NodeTypeIntegration NodeType = "integration"
	NodeTypeOutput      NodeType = "output"
	NodeTypeHuman       NodeType = "human"
)

// NodeHandler is the function that executes a node
type NodeHandler func(ctx context.Context, input map[string]interface{}) (map[string]interface{}, error)

// WorkflowExecution represents a running workflow instance
type WorkflowExecution struct {
	ID           string                 `json:"id"`
	WorkflowType string                 `json:"workflow_type"` // Changed from WorkflowID to match DB
	Status       string                 `json:"status"`        // Changed to string to match DB
	CurrentNode  string                 `json:"current_node"`
	StartedAt    time.Time              `json:"started_at"`    // Changed from StartTime to match DB
	CompletedAt  time.Time              `json:"completed_at"`  // Changed from EndTime to match DB
	InputData    string                 `json:"input_data"`    // Changed to string to match DB
	OutputData   string                 `json:"output_data"`   // Changed to string to match DB
	NodeResults  map[string]interface{} `json:"node_results"`
	ErrorMessage string                 `json:"error_message,omitempty"`
	CreatedAt    time.Time              `json:"created_at"`    // Added to match DB
	Metadata     map[string]interface{} `json:"metadata"`
}

// ExecutionStatus represents the status of a workflow execution
type ExecutionStatus string

const (
	StatusPending   ExecutionStatus = "pending"
	StatusRunning   ExecutionStatus = "running"
	StatusCompleted ExecutionStatus = "completed"
	StatusFailed    ExecutionStatus = "failed"
	StatusPaused    ExecutionStatus = "paused"
)

// NewWorkflowService creates a new LangGraph workflow service
func NewWorkflowService(config *LangGraphConfig, db *sql.DB, logger log.Logger) *WorkflowService {
	log := log.NewHelper(logger)

	service := &WorkflowService{
		log:       log,
		workflows: make(map[string]*HVACWorkflow),
		config:    config,
		db:        db,
	}

	// Initialize predefined HVAC workflows
	service.initializeHVACWorkflows()

	log.Info("🕸️ LangGraph Workflow Service initialized successfully")
	return service
}

// initializeHVACWorkflows sets up predefined HVAC business workflows
func (s *WorkflowService) initializeHVACWorkflows() {
	// Customer Onboarding Workflow
	s.registerWorkflow(&HVACWorkflow{
		ID:          "customer_onboarding",
		Name:        "Customer Onboarding Process",
		Description: "Complete workflow for onboarding new HVAC customers",
		Nodes: map[string]*WorkflowNode{
			"start": {
				ID:          "start",
				Type:        NodeTypeInput,
				Name:        "Customer Information Input",
				Description: "Collect initial customer information",
				Handler:     s.handleCustomerInput,
				Timeout:     30 * time.Second,
			},
			"validate": {
				ID:          "validate",
				Type:        NodeTypeAIAnalysis,
				Name:        "Validate Customer Data",
				Description: "AI validation of customer information completeness",
				Handler:     s.handleDataValidation,
				Timeout:     10 * time.Second,
			},
			"credit_check": {
				ID:          "credit_check",
				Type:        NodeTypeIntegration,
				Name:        "Credit Check",
				Description: "Perform credit check through external service",
				Handler:     s.handleCreditCheck,
				Timeout:     60 * time.Second,
			},
			"risk_assessment": {
				ID:          "risk_assessment",
				Type:        NodeTypeAIAnalysis,
				Name:        "Risk Assessment",
				Description: "AI-powered customer risk assessment",
				Handler:     s.handleRiskAssessment,
				Timeout:     15 * time.Second,
			},
			"approval_decision": {
				ID:          "approval_decision",
				Type:        NodeTypeDecision,
				Name:        "Approval Decision",
				Description: "Decide on customer approval based on risk assessment",
				Handler:     s.handleApprovalDecision,
				Timeout:     5 * time.Second,
			},
			"create_account": {
				ID:          "create_account",
				Type:        NodeTypeAction,
				Name:        "Create Customer Account",
				Description: "Create customer account in CRM system",
				Handler:     s.handleAccountCreation,
				Timeout:     20 * time.Second,
			},
			"send_welcome": {
				ID:          "send_welcome",
				Type:        NodeTypeAction,
				Name:        "Send Welcome Package",
				Description: "Send welcome email and materials to customer",
				Handler:     s.handleWelcomePackage,
				Timeout:     10 * time.Second,
			},
			"manual_review": {
				ID:          "manual_review",
				Type:        NodeTypeHuman,
				Name:        "Manual Review Required",
				Description: "Human review for edge cases",
				Handler:     s.handleManualReview,
				Timeout:     24 * time.Hour,
			},
			"complete": {
				ID:          "complete",
				Type:        NodeTypeOutput,
				Name:        "Onboarding Complete",
				Description: "Customer onboarding completed successfully",
				Handler:     s.handleCompletion,
				Timeout:     5 * time.Second,
			},
		},
		Edges: []WorkflowEdge{
			{From: "start", To: "validate"},
			{From: "validate", To: "credit_check", Condition: "data_valid"},
			{From: "validate", To: "manual_review", Condition: "data_invalid"},
			{From: "credit_check", To: "risk_assessment"},
			{From: "risk_assessment", To: "approval_decision"},
			{From: "approval_decision", To: "create_account", Condition: "approved"},
			{From: "approval_decision", To: "manual_review", Condition: "needs_review"},
			{From: "create_account", To: "send_welcome"},
			{From: "send_welcome", To: "complete"},
			{From: "manual_review", To: "create_account", Condition: "approved"},
			{From: "manual_review", To: "complete", Condition: "rejected"},
		},
		StartNode: "start",
		EndNodes:  []string{"complete"},
	})

	// Emergency Service Workflow
	s.registerWorkflow(&HVACWorkflow{
		ID:          "emergency_service",
		Name:        "Emergency Service Response",
		Description: "Rapid response workflow for HVAC emergencies",
		Nodes: map[string]*WorkflowNode{
			"emergency_intake": {
				ID:          "emergency_intake",
				Type:        NodeTypeInput,
				Name:        "Emergency Call Intake",
				Description: "Collect emergency service request details",
				Handler:     s.handleEmergencyIntake,
				Timeout:     60 * time.Second,
			},
			"severity_assessment": {
				ID:          "severity_assessment",
				Type:        NodeTypeAIAnalysis,
				Name:        "Severity Assessment",
				Description: "AI assessment of emergency severity",
				Handler:     s.handleSeverityAssessment,
				Timeout:     30 * time.Second,
			},
			"technician_dispatch": {
				ID:          "technician_dispatch",
				Type:        NodeTypeAction,
				Name:        "Dispatch Technician",
				Description: "Find and dispatch nearest available technician",
				Handler:     s.handleTechnicianDispatch,
				Timeout:     120 * time.Second,
			},
			"customer_notification": {
				ID:          "customer_notification",
				Type:        NodeTypeAction,
				Name:        "Notify Customer",
				Description: "Send ETA and technician details to customer",
				Handler:     s.handleCustomerNotification,
				Timeout:     30 * time.Second,
			},
			"service_completion": {
				ID:          "service_completion",
				Type:        NodeTypeOutput,
				Name:        "Service Completed",
				Description: "Emergency service completed",
				Handler:     s.handleServiceCompletion,
				Timeout:     10 * time.Second,
			},
		},
		Edges: []WorkflowEdge{
			{From: "emergency_intake", To: "severity_assessment"},
			{From: "severity_assessment", To: "technician_dispatch"},
			{From: "technician_dispatch", To: "customer_notification"},
			{From: "customer_notification", To: "service_completion"},
		},
		StartNode: "emergency_intake",
		EndNodes:  []string{"service_completion"},
	})

	s.log.Info("🕸️ HVAC workflows initialized successfully")
}

// registerWorkflow adds a workflow to the service
func (s *WorkflowService) registerWorkflow(workflow *HVACWorkflow) {
	s.workflows[workflow.ID] = workflow
	s.log.Infof("📝 Registered workflow: %s", workflow.Name)
}

// ExecuteWorkflow starts execution of a workflow
func (s *WorkflowService) ExecuteWorkflow(ctx context.Context, workflowID string, input map[string]interface{}) (*WorkflowExecution, error) {
	s.log.WithContext(ctx).Infof("🚀 Starting workflow execution: %s", workflowID)

	workflow, exists := s.workflows[workflowID]
	if !exists {
		return nil, fmt.Errorf("workflow %s not found", workflowID)
	}

	execution := &WorkflowExecution{
		ID:           fmt.Sprintf("%s_%d", workflowID, time.Now().Unix()),
		WorkflowType: workflowID,
		Status:       string(StatusRunning),
		CurrentNode:  workflow.StartNode,
		StartedAt:    time.Now(),
		InputData:    s.mapToJSON(input),
		NodeResults:  make(map[string]interface{}),
		Metadata:     make(map[string]interface{}),
	}

	// Execute workflow asynchronously
	go s.executeWorkflowAsync(ctx, workflow, execution)

	return execution, nil
}

// executeWorkflowAsync executes the workflow in a separate goroutine
func (s *WorkflowService) executeWorkflowAsync(ctx context.Context, workflow *HVACWorkflow, execution *WorkflowExecution) {
	defer func() {
		if r := recover(); r != nil {
			execution.Status = string(StatusFailed)
			execution.ErrorMessage = fmt.Sprintf("Workflow panic: %v", r)
			execution.CompletedAt = time.Now()
			s.log.Errorf("❌ Workflow %s panicked: %v", workflow.ID, r)
		}
	}()

	currentNodeID := workflow.StartNode
	nodeInput := execution.InputData

	for {
		// Check if we've reached an end node
		for _, endNode := range workflow.EndNodes {
			if currentNodeID == endNode {
				execution.Status = string(StatusCompleted)
				execution.CompletedAt = time.Now()
				s.log.Infof("✅ Workflow %s completed successfully", workflow.ID)
				return
			}
		}

		// Get current node
		node, exists := workflow.Nodes[currentNodeID]
		if !exists {
			execution.Status = string(StatusFailed)
			execution.ErrorMessage = fmt.Sprintf("Node %s not found", currentNodeID)
			execution.CompletedAt = time.Now()
			return
		}

		// Execute node
		s.log.Infof("🔄 Executing node: %s", node.Name)
		execution.CurrentNode = currentNodeID

		nodeCtx, cancel := context.WithTimeout(ctx, node.Timeout)
		nodeOutput, err := node.Handler(nodeCtx, nodeInput)
		cancel()

		if err != nil {
			execution.Status = string(StatusFailed)
			execution.ErrorMessage = fmt.Sprintf("Node %s failed: %v", currentNodeID, err)
			execution.CompletedAt = time.Now()
			s.log.Errorf("❌ Node %s failed: %v", currentNodeID, err)
			return
		}

		// Store node result
		execution.NodeResults[currentNodeID] = nodeOutput

		// Find next node
		nextNodeID := s.findNextNode(workflow, currentNodeID, nodeOutput)
		if nextNodeID == "" {
			execution.Status = string(StatusFailed)
			execution.ErrorMessage = fmt.Sprintf("No next node found from %s", currentNodeID)
			execution.CompletedAt = time.Now()
			return
		}

		currentNodeID = nextNodeID
		nodeInput = nodeOutput
	}
}

// findNextNode determines the next node based on current node output
func (s *WorkflowService) findNextNode(workflow *HVACWorkflow, currentNodeID string, nodeOutput map[string]interface{}) string {
	for _, edge := range workflow.Edges {
		if edge.From == currentNodeID {
			// If no condition, take this edge
			if edge.Condition == "" {
				return edge.To
			}

			// Check condition (simplified condition checking)
			if condition, exists := nodeOutput["condition"]; exists {
				if condition == edge.Condition {
					return edge.To
				}
			}
		}
	}
	return ""
}

// GetWorkflowStatus returns the current status of a workflow execution - REAL DATA INTEGRATION
func (s *WorkflowService) GetWorkflowStatus(ctx context.Context, executionID string) (*WorkflowExecution, error) {
	// Query workflow execution from database
	var execution WorkflowExecution
	var startedAt, completedAt sql.NullTime

	err := s.db.QueryRowContext(ctx, `
		SELECT execution_id, workflow_type, status, input_data, output_data,
			   started_at, completed_at, error_message, created_at
		FROM workflow_executions
		WHERE execution_id = $1
	`, executionID).Scan(
		&execution.ID, &execution.WorkflowType, &execution.Status,
		&execution.InputData, &execution.OutputData,
		&startedAt, &completedAt, &execution.ErrorMessage, &execution.CreatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("workflow execution %s not found", executionID)
		}
		return nil, fmt.Errorf("failed to get workflow status: %w", err)
	}

	if startedAt.Valid {
		execution.StartedAt = startedAt.Time
	}
	if completedAt.Valid {
		execution.CompletedAt = completedAt.Time
	}

	return &execution, nil
}

// ListWorkflows returns all available workflows
func (s *WorkflowService) ListWorkflows() map[string]*HVACWorkflow {
	return s.workflows
}

// Node Handlers (simplified implementations)
func (s *WorkflowService) handleCustomerInput(ctx context.Context, input map[string]interface{}) (map[string]interface{}, error) {
	s.log.Info("📝 Processing customer input")
	return map[string]interface{}{
		"condition": "data_valid",
		"customer_data": input,
	}, nil
}

func (s *WorkflowService) handleDataValidation(ctx context.Context, input map[string]interface{}) (map[string]interface{}, error) {
	s.log.Info("✅ Validating customer data")
	return map[string]interface{}{
		"condition": "data_valid",
		"validation_result": "passed",
	}, nil
}

func (s *WorkflowService) handleCreditCheck(ctx context.Context, input map[string]interface{}) (map[string]interface{}, error) {
	s.log.Info("💳 Performing credit check")
	return map[string]interface{}{
		"credit_score": 750,
		"credit_status": "approved",
	}, nil
}

func (s *WorkflowService) handleRiskAssessment(ctx context.Context, input map[string]interface{}) (map[string]interface{}, error) {
	s.log.Info("🎯 Performing risk assessment")
	return map[string]interface{}{
		"risk_level": "low",
		"risk_score": 0.2,
	}, nil
}

func (s *WorkflowService) handleApprovalDecision(ctx context.Context, input map[string]interface{}) (map[string]interface{}, error) {
	s.log.Info("✅ Making approval decision")
	return map[string]interface{}{
		"condition": "approved",
		"decision": "approved",
	}, nil
}

func (s *WorkflowService) handleAccountCreation(ctx context.Context, input map[string]interface{}) (map[string]interface{}, error) {
	s.log.Info("👤 Creating customer account")
	return map[string]interface{}{
		"account_id": "CUST_12345",
		"account_created": true,
	}, nil
}

func (s *WorkflowService) handleWelcomePackage(ctx context.Context, input map[string]interface{}) (map[string]interface{}, error) {
	s.log.Info("📧 Sending welcome package")
	return map[string]interface{}{
		"welcome_sent": true,
		"email_sent": true,
	}, nil
}

func (s *WorkflowService) handleManualReview(ctx context.Context, input map[string]interface{}) (map[string]interface{}, error) {
	s.log.Info("👨‍💼 Manual review required")
	return map[string]interface{}{
		"condition": "approved",
		"review_result": "approved",
	}, nil
}

func (s *WorkflowService) handleCompletion(ctx context.Context, input map[string]interface{}) (map[string]interface{}, error) {
	s.log.Info("🎉 Workflow completed")
	return map[string]interface{}{
		"status": "completed",
		"completion_time": time.Now(),
	}, nil
}

func (s *WorkflowService) handleEmergencyIntake(ctx context.Context, input map[string]interface{}) (map[string]interface{}, error) {
	s.log.Info("🚨 Processing emergency intake")
	return map[string]interface{}{
		"emergency_type": "heating_failure",
		"severity": "high",
	}, nil
}

func (s *WorkflowService) handleSeverityAssessment(ctx context.Context, input map[string]interface{}) (map[string]interface{}, error) {
	s.log.Info("📊 Assessing emergency severity")
	return map[string]interface{}{
		"severity_score": 8.5,
		"priority": "high",
	}, nil
}

func (s *WorkflowService) handleTechnicianDispatch(ctx context.Context, input map[string]interface{}) (map[string]interface{}, error) {
	s.log.Info("🚗 Dispatching technician")
	return map[string]interface{}{
		"technician_id": "TECH_001",
		"eta": "30 minutes",
	}, nil
}

func (s *WorkflowService) handleCustomerNotification(ctx context.Context, input map[string]interface{}) (map[string]interface{}, error) {
	s.log.Info("📱 Notifying customer")
	return map[string]interface{}{
		"notification_sent": true,
		"sms_sent": true,
	}, nil
}

func (s *WorkflowService) handleServiceCompletion(ctx context.Context, input map[string]interface{}) (map[string]interface{}, error) {
	s.log.Info("✅ Service completed")
	return map[string]interface{}{
		"service_completed": true,
		"completion_time": time.Now(),
	}, nil
}
