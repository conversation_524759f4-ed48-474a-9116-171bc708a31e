package executive

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"

	"gobackend-hvac-kratos/internal/ai"
	"gobackend-hvac-kratos/internal/data"
	"gobackend-hvac-kratos/internal/langchain"
)

// ✍️ DraftEngine - AI-powered email response generation
type DraftEngine struct {
	log              *log.Helper
	db               *gorm.DB
	aiService        *ai.EnhancedService
	langchainService *langchain.Service
	config           *Config
	templateManager  *ResponseTemplateManager
	qualityChecker   *ResponseQualityChecker
}

// 📝 DraftResult represents the result of response generation
type DraftResult struct {
	DraftID          int64                    `json:"draft_id"`
	Subject          string                   `json:"subject"`
	Body             string                   `json:"body"`
	HTMLBody         string                   `json:"html_body"`
	ResponseType     data.ResponseType        `json:"response_type"`
	ResponseTone     data.ResponseTone        `json:"response_tone"`
	ToAddresses      []string                 `json:"to_addresses"`
	CCAddresses      []string                 `json:"cc_addresses"`
	QualityScore     float64                  `json:"quality_score"`
	RelevanceScore   float64                  `json:"relevance_score"`
	ToneMatchScore   float64                  `json:"tone_match_score"`
	Status           data.DraftStatus         `json:"status"`
	GenerationPrompt string                   `json:"generation_prompt"`
	AIModelUsed      string                   `json:"ai_model_used"`
	ProcessingTimeMs int                      `json:"processing_time_ms"`
	Success          bool                     `json:"success"`
	ErrorMessage     string                   `json:"error_message,omitempty"`
}

// 📋 Response template manager
type ResponseTemplateManager struct {
	templates map[string]*ResponseTemplate
}

// 📄 Response template
type ResponseTemplate struct {
	Name        string            `json:"name"`
	Subject     string            `json:"subject"`
	Body        string            `json:"body"`
	Tone        data.ResponseTone `json:"tone"`
	Variables   []string          `json:"variables"`
	Conditions  []string          `json:"conditions"`
}

// ✅ Response quality checker
type ResponseQualityChecker struct {
	qualityMetrics map[string]float64
}

// NewDraftEngine creates a new email response draft engine
func NewDraftEngine(
	db *gorm.DB,
	aiService *ai.EnhancedService,
	langchainService *langchain.Service,
	config *Config,
	logger log.Logger,
) *DraftEngine {
	helper := log.NewHelper(logger)

	return &DraftEngine{
		log:              helper,
		db:               db,
		aiService:        aiService,
		langchainService: langchainService,
		config:           config,
		templateManager:  newResponseTemplateManager(),
		qualityChecker:   newResponseQualityChecker(),
	}
}

// ✍️ GenerateResponse - Main response generation function
func (d *DraftEngine) GenerateResponse(ctx context.Context, email *data.Email, triage *TriageResult) (*DraftResult, error) {
	startTime := time.Now()
	d.log.WithContext(ctx).Infof("✍️ Generating response for email: %s", email.Subject)

	result := &DraftResult{
		ResponseType: data.ResponseTypeReply,
		ResponseTone: data.ResponseTone(d.config.DefaultResponseTone),
		AIModelUsed:  d.config.PrimaryAIModel,
		Status:       data.DraftStatusDraft,
	}

	// 1. Determine response strategy based on triage
	strategy, err := d.determineResponseStrategy(ctx, email, triage)
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("Failed to determine response strategy: %v", err)
		return result, err
	}

	// 2. Check for existing templates
	template := d.templateManager.findMatchingTemplate(triage)

	// 3. Generate response using AI
	var responseContent *ResponseContent
	if template != nil {
		d.log.WithContext(ctx).Infof("📋 Using template: %s", template.Name)
		responseContent, err = d.generateFromTemplate(ctx, email, triage, template)
	} else {
		d.log.WithContext(ctx).Info("🤖 Generating custom AI response")
		responseContent, err = d.generateCustomResponse(ctx, email, triage, strategy)
	}

	if err != nil {
		result.ErrorMessage = fmt.Sprintf("Response generation failed: %v", err)
		return result, err
	}

	// 4. Post-process and enhance response
	err = d.postProcessResponse(ctx, responseContent, email, triage)
	if err != nil {
		d.log.WithContext(ctx).Warnf("Post-processing failed: %v", err)
	}

	// 5. Perform quality checks
	qualityScores := d.qualityChecker.assessQuality(responseContent, email, triage)

	// 6. Populate result
	result.Subject = responseContent.Subject
	result.Body = responseContent.Body
	result.HTMLBody = responseContent.HTMLBody
	result.ToAddresses = d.determineRecipients(email, triage)
	result.CCAddresses = d.determineCCRecipients(email, triage)
	result.QualityScore = qualityScores.Overall
	result.RelevanceScore = qualityScores.Relevance
	result.ToneMatchScore = qualityScores.ToneMatch
	result.GenerationPrompt = responseContent.GenerationPrompt

	// 7. Store draft in database
	draftID, err := d.storeDraft(ctx, email.ID, result, triage)
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("Failed to store draft: %v", err)
		return result, err
	}
	result.DraftID = draftID

	result.ProcessingTimeMs = int(time.Since(startTime).Milliseconds())
	result.Success = true

	d.log.WithContext(ctx).Infof("✅ Response generated successfully (quality: %.2f)", result.QualityScore)
	return result, nil
}

// 🎯 Determine response strategy based on triage results
func (d *DraftEngine) determineResponseStrategy(ctx context.Context, email *data.Email, triage *TriageResult) (*ResponseStrategy, error) {
	strategy := &ResponseStrategy{
		Approach:     "standard",
		Tone:         data.ResponseTone(d.config.DefaultResponseTone),
		Priority:     triage.Priority,
		IncludeInfo:  true,
		RequireApproval: d.config.RequireApproval,
	}

	// Adjust strategy based on triage results
	switch triage.HVACServiceType {
	case "emergency":
		strategy.Approach = "emergency"
		strategy.Tone = data.ResponseToneUrgent
		strategy.RequireApproval = false // Emergency responses can be auto-sent

	case "quote":
		strategy.Approach = "sales"
		strategy.Tone = data.ResponseToneProfessional
		strategy.IncludeInfo = true

	case "complaint":
		strategy.Approach = "service_recovery"
		strategy.Tone = data.ResponseToneFriendly
		strategy.RequireApproval = true

	case "maintenance":
		strategy.Approach = "service"
		strategy.Tone = data.ResponseToneProfessional
	}

	// Adjust based on sentiment
	switch triage.Sentiment {
	case data.SentimentAngry:
		strategy.Tone = data.ResponseToneFriendly
		strategy.Approach = "service_recovery"
		strategy.RequireApproval = true

	case data.SentimentNegative:
		strategy.Tone = data.ResponseToneFriendly
		strategy.RequireApproval = true

	case data.SentimentPositive:
		strategy.Tone = data.ResponseToneFriendly
	}

	return strategy, nil
}

// 📋 Generate response from template
func (d *DraftEngine) generateFromTemplate(ctx context.Context, email *data.Email, triage *TriageResult, template *ResponseTemplate) (*ResponseContent, error) {
	// Extract variables from email and triage
	variables := d.extractTemplateVariables(email, triage)

	// Replace template variables
	subject := d.replaceTemplateVariables(template.Subject, variables)
	body := d.replaceTemplateVariables(template.Body, variables)

	// Enhance with AI if needed
	if d.shouldEnhanceTemplate(template, triage) {
		enhancedContent, err := d.enhanceTemplateWithAI(ctx, subject, body, email, triage)
		if err != nil {
			d.log.WithContext(ctx).Warnf("Template enhancement failed: %v", err)
		} else {
			subject = enhancedContent.Subject
			body = enhancedContent.Body
		}
	}

	return &ResponseContent{
		Subject:          subject,
		Body:             body,
		HTMLBody:         d.convertToHTML(body),
		GenerationPrompt: fmt.Sprintf("Template: %s", template.Name),
		Source:           "template",
	}, nil
}

// 🤖 Generate custom AI response
func (d *DraftEngine) generateCustomResponse(ctx context.Context, email *data.Email, triage *TriageResult, strategy *ResponseStrategy) (*ResponseContent, error) {
	// Prepare context for AI generation
	context := d.prepareAIContext(email, triage, strategy)

	// Generate prompt
	prompt := d.buildGenerationPrompt(email, triage, strategy, context)

	// Use LangChain for advanced response generation
	chainRequest := &langchain.EmailResponseRequest{
		OriginalEmail:    email.Body,
		EmailSubject:     email.Subject,
		SenderEmail:      email.From,
		TriageResults:    d.convertTriageToChainFormat(triage),
		ResponseStrategy: d.convertStrategyToChainFormat(strategy),
		Context:          context,
		MaxLength:        d.config.MaxResponseLength,
		Tone:             string(strategy.Tone),
	}

	// Generate response using LangChain
	chainResponse, err := d.langchainService.GenerateEmailResponse(ctx, chainRequest)
	if err != nil {
		// Fallback to direct AI service
		return d.generateWithDirectAI(ctx, prompt, email, triage)
	}

	return &ResponseContent{
		Subject:          chainResponse.Subject,
		Body:             chainResponse.Body,
		HTMLBody:         chainResponse.HTMLBody,
		GenerationPrompt: prompt,
		Source:           "langchain",
		Confidence:       chainResponse.Confidence,
	}, nil
}

// 🔧 Post-process response
func (d *DraftEngine) postProcessResponse(ctx context.Context, content *ResponseContent, email *data.Email, triage *TriageResult) error {
	// 1. Add signature
	content.Body = d.addSignature(content.Body)

	// 2. Add disclaimer if needed
	if d.shouldAddDisclaimer(triage) {
		content.Body = d.addDisclaimer(content.Body)
	}

	// 3. Add contact information
	content.Body = d.addContactInfo(content.Body, triage)

	// 4. Format HTML version
	content.HTMLBody = d.convertToHTML(content.Body)

	// 5. Validate content
	return d.validateResponseContent(content)
}

// 💾 Store draft in database
func (d *DraftEngine) storeDraft(ctx context.Context, emailID int64, result *DraftResult, triage *TriageResult) (int64, error) {
	draft := &data.EmailResponseDraft{
		EmailID:           emailID,
		DraftSubject:      &result.Subject,
		DraftBody:         result.Body,
		DraftHTMLBody:     &result.HTMLBody,
		ResponseType:      result.ResponseType,
		ResponseTone:      result.ResponseTone,
		ToAddresses:       result.ToAddresses,
		CCAddresses:       result.CCAddresses,
		AIModelUsed:       result.AIModelUsed,
		GenerationPrompt:  &result.GenerationPrompt,
		QualityScore:      &result.QualityScore,
		RelevanceScore:    &result.RelevanceScore,
		ToneMatchScore:    &result.ToneMatchScore,
		Status:            result.Status,
	}

	// Set generation context
	generationContext := map[string]interface{}{
		"triage_action":      triage.Action,
		"priority":           triage.Priority,
		"hvac_service_type":  triage.HVACServiceType,
		"sentiment":          triage.Sentiment,
		"confidence":         triage.Confidence,
		"processing_time_ms": result.ProcessingTimeMs,
	}
	draft.GenerationContext = generationContext

	err := d.db.WithContext(ctx).Create(draft).Error
	if err != nil {
		return 0, fmt.Errorf("failed to create draft: %w", err)
	}

	return draft.ID, nil
}

// ==========================================
// HELPER TYPES AND STRUCTURES
// ==========================================

type ResponseStrategy struct {
	Approach        string            `json:"approach"`
	Tone            data.ResponseTone `json:"tone"`
	Priority        data.PriorityLevel `json:"priority"`
	IncludeInfo     bool              `json:"include_info"`
	RequireApproval bool              `json:"require_approval"`
}

type ResponseContent struct {
	Subject          string  `json:"subject"`
	Body             string  `json:"body"`
	HTMLBody         string  `json:"html_body"`
	GenerationPrompt string  `json:"generation_prompt"`
	Source           string  `json:"source"`
	Confidence       float64 `json:"confidence"`
}

type QualityScores struct {
	Overall   float64 `json:"overall"`
	Relevance float64 `json:"relevance"`
	ToneMatch float64 `json:"tone_match"`
	Grammar   float64 `json:"grammar"`
	Clarity   float64 `json:"clarity"`
}

// ==========================================
// TEMPLATE MANAGER IMPLEMENTATION
// ==========================================

func newResponseTemplateManager() *ResponseTemplateManager {
	templates := map[string]*ResponseTemplate{
		"emergency_response": {
			Name:    "Emergency Response",
			Subject: "Re: {original_subject} - Emergency Service Dispatch",
			Body: `Dear {customer_name},

Thank you for contacting us regarding your HVAC emergency. We understand the urgency of your situation and are taking immediate action.

Emergency Details:
- Service Type: {service_type}
- Equipment: {equipment_type}
- Priority: URGENT

We are dispatching a technician to your location and will contact you within the next 30 minutes with an estimated arrival time.

For immediate assistance, please call our emergency hotline: (555) 123-HVAC

Best regards,
Emergency Response Team`,
			Tone: data.ResponseToneUrgent,
			Variables: []string{"customer_name", "original_subject", "service_type", "equipment_type"},
			Conditions: []string{"hvac_service_type=emergency"},
		},

		"quote_request": {
			Name:    "Quote Request Response",
			Subject: "Re: {original_subject} - HVAC Service Quote",
			Body: `Dear {customer_name},

Thank you for your interest in our HVAC services. We would be happy to provide you with a detailed quote for {service_type}.

To prepare an accurate estimate, we will need to schedule a brief consultation to assess your specific needs and requirements.

Available time slots:
- {available_slots}

Our quote includes:
- Detailed assessment of your HVAC system
- Transparent pricing with no hidden fees
- Professional recommendations
- 1-year warranty on all work

Please reply with your preferred time, or call us at (555) 123-HVAC to schedule.

Best regards,
Sales Team`,
			Tone: data.ResponseToneProfessional,
			Variables: []string{"customer_name", "original_subject", "service_type", "available_slots"},
			Conditions: []string{"detected_intent=request_quote"},
		},

		"complaint_acknowledgment": {
			Name:    "Complaint Acknowledgment",
			Subject: "Re: {original_subject} - We're Here to Help",
			Body: `Dear {customer_name},

Thank you for bringing this matter to our attention. We sincerely apologize for any inconvenience you have experienced with our service.

Your feedback is extremely valuable to us, and we are committed to resolving this issue promptly and to your complete satisfaction.

Next Steps:
1. A senior technician will review your case within 24 hours
2. We will contact you to discuss the situation and our proposed solution
3. We will ensure this issue is resolved to your satisfaction

We appreciate your patience and the opportunity to make this right.

Best regards,
Customer Service Manager`,
			Tone: data.ResponseToneFriendly,
			Variables: []string{"customer_name", "original_subject"},
			Conditions: []string{"sentiment=negative", "detected_intent=complaint"},
		},
	}

	return &ResponseTemplateManager{
		templates: templates,
	}
}

func (tm *ResponseTemplateManager) findMatchingTemplate(triage *TriageResult) *ResponseTemplate {
	// Check emergency first
	if triage.HVACServiceType == "emergency" {
		return tm.templates["emergency_response"]
	}

	// Check for quote requests
	if strings.Contains(strings.ToLower(triage.DetectedIntent), "quote") {
		return tm.templates["quote_request"]
	}

	// Check for complaints
	if triage.Sentiment == data.SentimentNegative || triage.Sentiment == data.SentimentAngry {
		return tm.templates["complaint_acknowledgment"]
	}

	return nil
}

// ==========================================
// QUALITY CHECKER IMPLEMENTATION
// ==========================================

func newResponseQualityChecker() *ResponseQualityChecker {
	return &ResponseQualityChecker{
		qualityMetrics: map[string]float64{
			"min_length":     50,   // Minimum response length
			"max_length":     2000, // Maximum response length
			"politeness":     0.8,  // Politeness score threshold
			"relevance":      0.7,  // Relevance score threshold
			"tone_match":     0.8,  // Tone matching threshold
		},
	}
}

func (qc *ResponseQualityChecker) assessQuality(content *ResponseContent, email *data.Email, triage *TriageResult) *QualityScores {
	scores := &QualityScores{}

	// Length check
	bodyLength := len(content.Body)
	if bodyLength >= int(qc.qualityMetrics["min_length"]) && bodyLength <= int(qc.qualityMetrics["max_length"]) {
		scores.Clarity += 0.3
	}

	// Politeness check
	if qc.checkPoliteness(content.Body) {
		scores.ToneMatch += 0.4
	}

	// Relevance check
	scores.Relevance = qc.checkRelevance(content.Body, email.Body, triage)

	// Grammar check (simplified)
	scores.Grammar = qc.checkGrammar(content.Body)

	// Overall score
	scores.Overall = (scores.Relevance + scores.ToneMatch + scores.Grammar + scores.Clarity) / 4

	return scores
}

func (qc *ResponseQualityChecker) checkPoliteness(body string) bool {
	politeWords := []string{"please", "thank you", "appreciate", "sincerely", "regards"}
	lowerBody := strings.ToLower(body)

	for _, word := range politeWords {
		if strings.Contains(lowerBody, word) {
			return true
		}
	}
	return false
}

func (qc *ResponseQualityChecker) checkRelevance(responseBody, originalBody string, triage *TriageResult) float64 {
	// Simple keyword matching for relevance
	originalWords := strings.Fields(strings.ToLower(originalBody))
	responseWords := strings.Fields(strings.ToLower(responseBody))

	matches := 0
	for _, origWord := range originalWords {
		if len(origWord) > 3 { // Only check meaningful words
			for _, respWord := range responseWords {
				if origWord == respWord {
					matches++
					break
				}
			}
		}
	}

	if len(originalWords) == 0 {
		return 0.5
	}

	relevance := float64(matches) / float64(len(originalWords))
	if relevance > 1.0 {
		relevance = 1.0
	}

	return relevance
}

func (qc *ResponseQualityChecker) checkGrammar(body string) float64 {
	// Simplified grammar check
	sentences := strings.Split(body, ".")
	if len(sentences) < 2 {
		return 0.5
	}

	// Check for basic sentence structure
	validSentences := 0
	for _, sentence := range sentences {
		sentence = strings.TrimSpace(sentence)
		if len(sentence) > 10 && strings.Contains(sentence, " ") {
			validSentences++
		}
	}

	return float64(validSentences) / float64(len(sentences))
}

// ==========================================
// UTILITY FUNCTIONS
// ==========================================

func (d *DraftEngine) determineRecipients(email *data.Email, triage *TriageResult) []string {
	// Default to reply to sender
	return []string{email.From}
}

func (d *DraftEngine) determineCCRecipients(email *data.Email, triage *TriageResult) []string {
	// Add CC recipients based on triage results
	var ccList []string

	// For high priority items, CC the manager
	if triage.Priority == data.PriorityUrgent || triage.Priority == data.PriorityHigh {
		ccList = append(ccList, "<EMAIL>")
	}

	return ccList
}

func (d *DraftEngine) addSignature(body string) string {
	signature := `

Best regards,
HVAC Service Team
Phone: (555) 123-HVAC
Email: <EMAIL>
Website: www.hvac.com`

	return body + signature
}

func (d *DraftEngine) addDisclaimer(body string) string {
	disclaimer := `

---
This email was generated by our AI assistant. If you have any concerns, please contact us directly.`

	return body + disclaimer
}

func (d *DraftEngine) addContactInfo(body string, triage *TriageResult) string {
	if triage.Priority == data.PriorityUrgent {
		emergency := `

EMERGENCY CONTACT: (555) 123-EMRG (available 24/7)`
		return body + emergency
	}
	return body
}

func (d *DraftEngine) convertToHTML(body string) string {
	// Simple text to HTML conversion
	html := strings.ReplaceAll(body, "\n", "<br>")
	return fmt.Sprintf("<html><body>%s</body></html>", html)
}

func (d *DraftEngine) shouldAddDisclaimer(triage *TriageResult) bool {
	// Add disclaimer for AI-generated responses
	return triage.Confidence < 0.9
}

func (d *DraftEngine) shouldEnhanceTemplate(template *ResponseTemplate, triage *TriageResult) bool {
	// Enhance templates for complex situations
	return triage.Priority == data.PriorityUrgent || triage.Sentiment == data.SentimentAngry
}

func (d *DraftEngine) validateResponseContent(content *ResponseContent) error {
	if content.Subject == "" {
		return fmt.Errorf("response subject cannot be empty")
	}
	if content.Body == "" {
		return fmt.Errorf("response body cannot be empty")
	}
	if len(content.Body) > d.config.MaxResponseLength {
		return fmt.Errorf("response body exceeds maximum length")
	}
	return nil
}

// ==========================================
// MISSING METHODS IMPLEMENTATION
// ==========================================

// extractTemplateVariables extracts variables from email and triage for template replacement
func (d *DraftEngine) extractTemplateVariables(email *data.Email, triage *TriageResult) map[string]string {
	variables := make(map[string]string)

	// Basic email variables
	variables["original_subject"] = email.Subject
	variables["customer_name"] = d.extractCustomerName(email.From)
	variables["service_type"] = triage.HVACServiceType
	variables["equipment_type"] = triage.EquipmentType
	variables["customer_email"] = email.From

	// Triage-based variables
	variables["priority"] = string(triage.Priority)
	variables["sentiment"] = string(triage.Sentiment)
	variables["detected_intent"] = triage.DetectedIntent

	// Time-based variables
	variables["current_date"] = time.Now().Format("January 2, 2006")
	variables["current_time"] = time.Now().Format("3:04 PM")

	// Business variables
	variables["available_slots"] = d.getAvailableTimeSlots()

	return variables
}

// replaceTemplateVariables replaces template variables with actual values
func (d *DraftEngine) replaceTemplateVariables(template string, variables map[string]string) string {
	result := template

	for key, value := range variables {
		placeholder := "{" + key + "}"
		result = strings.ReplaceAll(result, placeholder, value)
	}

	return result
}

// enhanceTemplateWithAI enhances template content using AI
func (d *DraftEngine) enhanceTemplateWithAI(ctx context.Context, subject, body string, email *data.Email, triage *TriageResult) (*ResponseContent, error) {
	// Create enhancement request
	enhancementPrompt := fmt.Sprintf(`
	Please enhance this email response to be more personalized and appropriate:

	Subject: %s
	Body: %s

	Original customer email: %s
	Customer sentiment: %s
	Service type: %s
	Priority: %s

	Make it more engaging while maintaining professionalism.
	`, subject, body, email.Body, triage.Sentiment, triage.HVACServiceType, triage.Priority)

	// Use LangChain for enhancement
	enhancedResult, err := d.langchainService.AnalyzeContent(ctx, enhancementPrompt, "email_enhancement")
	if err != nil {
		return nil, fmt.Errorf("AI enhancement failed: %w", err)
	}

	// Parse enhanced content (simplified)
	enhancedSubject := subject
	enhancedBody := body

	if enhancedResult.Results != nil {
		if enhanced, ok := enhancedResult.Results["enhanced_content"].(string); ok {
			enhancedLines := strings.Split(enhanced, "\n")
			if len(enhancedLines) > 0 {
				enhancedSubject = enhancedLines[0]
			}
			if len(enhancedLines) > 1 {
				enhancedBody = strings.Join(enhancedLines[1:], "\n")
			}
		}
	}

	return &ResponseContent{
		Subject:    enhancedSubject,
		Body:       enhancedBody,
		HTMLBody:   d.convertToHTML(enhancedBody),
		Source:     "ai_enhanced",
		Confidence: enhancedResult.Confidence,
	}, nil
}

// Helper methods
func (d *DraftEngine) extractCustomerName(email string) string {
	// Extract name from email address (simplified)
	parts := strings.Split(email, "@")
	if len(parts) > 0 {
		namePart := parts[0]
		// Replace dots and underscores with spaces
		namePart = strings.ReplaceAll(namePart, ".", " ")
		namePart = strings.ReplaceAll(namePart, "_", " ")
		// Capitalize first letter of each word
		words := strings.Fields(namePart)
		for i, word := range words {
			if len(word) > 0 {
				words[i] = strings.ToUpper(string(word[0])) + strings.ToLower(word[1:])
			}
		}
		return strings.Join(words, " ")
	}
	return "Valued Customer"
}

func (d *DraftEngine) getAvailableTimeSlots() string {
	// Mock available time slots
	return "Monday 9:00 AM - 12:00 PM, Tuesday 2:00 PM - 5:00 PM, Wednesday 10:00 AM - 3:00 PM"
}