package executive

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"

	"gobackend-hvac-kratos/internal/ai"
	"gobackend-hvac-kratos/internal/data"
	"gobackend-hvac-kratos/internal/email"
	"gobackend-hvac-kratos/internal/langchain"
)

// 🧠 Executive AI Assistant Service
// Comprehensive AI-powered email automation and management system
type Service struct {
	log              *log.Helper
	db               *gorm.DB
	aiService        *ai.EnhancedService
	emailService     *email.EmailIntelligenceService
	langchainService *langchain.Service
	triageEngine     *TriageEngine
	draftEngine      *DraftEngine
	memoryBank       *MemoryBankService
	workflowEngine   *langchain.WorkflowEngine
	// TODO: Implement CalendarService and MetricsCollector
	// calendarService  *CalendarService
	// metricsCollector *MetricsCollector
	config           *Config
}

// 🔧 Executive AI Configuration
type Config struct {
	// AI Models Configuration
	PrimaryAIModel   string `json:"primary_ai_model"`
	FallbackAIModel  string `json:"fallback_ai_model"`
	ConfidenceThreshold float64 `json:"confidence_threshold"`

	// Triage Configuration
	AutoRespondThreshold    float64 `json:"auto_respond_threshold"`
	EscalationThreshold     float64 `json:"escalation_threshold"`
	MaxProcessingTimeMs     int     `json:"max_processing_time_ms"`

	// Response Generation
	DefaultResponseTone     string `json:"default_response_tone"`
	MaxResponseLength       int    `json:"max_response_length"`
	RequireApproval         bool   `json:"require_approval"`

	// Memory Bank Settings
	MemoryRetentionDays     int     `json:"memory_retention_days"`
	MinConfidenceForMemory  float64 `json:"min_confidence_for_memory"`

	// Workflow Settings
	EnableWorkflowAutomation bool `json:"enable_workflow_automation"`
	MaxConcurrentWorkflows   int  `json:"max_concurrent_workflows"`

	// Calendar Integration
	DefaultMeetingDuration   int    `json:"default_meeting_duration"`
	BusinessHoursStart       string `json:"business_hours_start"`
	BusinessHoursEnd         string `json:"business_hours_end"`
	Timezone                 string `json:"timezone"`

	// Performance Settings
	EnableMetricsCollection  bool `json:"enable_metrics_collection"`
	MetricsRetentionDays     int  `json:"metrics_retention_days"`
}

// 📧 Email Processing Request
type EmailProcessingRequest struct {
	EmailID          int64                  `json:"email_id"`
	ForceReprocessing bool                  `json:"force_reprocessing"`
	SkipWorkflows    bool                   `json:"skip_workflows"`
	Context          map[string]interface{} `json:"context"`
}

// 📊 Email Processing Result
type EmailProcessingResult struct {
	EmailID         int64                    `json:"email_id"`
	ProcessingTime  time.Duration            `json:"processing_time"`
	TriageResult    *TriageResult           `json:"triage_result"`
	DraftResult     *DraftResult            `json:"draft_result"`
	WorkflowResults []*WorkflowExecutionResult `json:"workflow_results"`
	MemoryUpdates   []*MemoryUpdate         `json:"memory_updates"`
	CalendarEvents  []*CalendarEventResult  `json:"calendar_events"`
	Metrics         *ProcessingMetrics      `json:"metrics"`
	Success         bool                    `json:"success"`
	ErrorMessage    string                  `json:"error_message,omitempty"`
}

// NewService creates a new Executive AI Assistant service
func NewService(
	db *gorm.DB,
	aiService *ai.EnhancedService,
	emailService *email.EmailIntelligenceService,
	langchainService *langchain.Service,
	config *Config,
	logger log.Logger,
) (*Service, error) {
	helper := log.NewHelper(logger)
	helper.Info("🧠 Initializing Executive AI Assistant Service...")

	// Initialize sub-services
	triageEngine := NewTriageEngine(db, aiService, langchainService, config, logger)
	draftEngine := NewDraftEngine(db, aiService, langchainService, config, logger)
	memoryBank := NewMemoryBankService(db, aiService, config, logger)
	workflowEngine := NewWorkflowEngine(db, aiService, config, logger)
	calendarService := NewCalendarService(db, config, logger)
	metricsCollector := NewMetricsCollector(db, config, logger)

	service := &Service{
		log:              helper,
		db:               db,
		aiService:        aiService,
		emailService:     emailService,
		langchainService: langchainService,
		triageEngine:     triageEngine,
		draftEngine:      draftEngine,
		memoryBank:       memoryBank,
		workflowEngine:   workflowEngine,
		calendarService:  calendarService,
		metricsCollector: metricsCollector,
		config:           config,
	}

	helper.Info("✅ Executive AI Assistant Service initialized successfully!")
	return service, nil
}

// 🚀 ProcessIncomingEmail - Main entry point for email processing
func (s *Service) ProcessIncomingEmail(ctx context.Context, req *EmailProcessingRequest) (*EmailProcessingResult, error) {
	startTime := time.Now()
	s.log.WithContext(ctx).Infof("🧠 Processing email ID: %d", req.EmailID)

	result := &EmailProcessingResult{
		EmailID:        req.EmailID,
		Success:        false,
		WorkflowResults: []*WorkflowExecutionResult{},
		MemoryUpdates:  []*MemoryUpdate{},
		CalendarEvents: []*CalendarEventResult{},
	}

	// 1. Retrieve email from database
	email, err := s.getEmailByID(ctx, req.EmailID)
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("Failed to retrieve email: %v", err)
		return result, err
	}

	// 2. Check if already processed (unless force reprocessing)
	if !req.ForceReprocessing {
		existingTriage, err := s.getExistingTriage(ctx, req.EmailID)
		if err == nil && existingTriage != nil {
			s.log.WithContext(ctx).Infof("Email already processed, skipping triage")
			result.TriageResult = s.convertTriageToResult(existingTriage)
			result.Success = true
			result.ProcessingTime = time.Since(startTime)
			return result, nil
		}
	}

	// 3. Perform AI-powered triage
	triageResult, err := s.triageEngine.TriageEmail(ctx, email)
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("Triage failed: %v", err)
		return result, err
	}
	result.TriageResult = triageResult

	// 4. Store triage results in database
	err = s.storeTriageResult(ctx, req.EmailID, triageResult)
	if err != nil {
		s.log.WithContext(ctx).Warnf("Failed to store triage result: %v", err)
	}

	// 5. Update memory bank with new insights
	memoryUpdates, err := s.memoryBank.ProcessEmailForMemory(ctx, email, triageResult)
	if err != nil {
		s.log.WithContext(ctx).Warnf("Memory bank update failed: %v", err)
	} else {
		result.MemoryUpdates = memoryUpdates
	}

	// 6. Process based on triage decision
	switch triageResult.Action {
	case data.TriageActionRespond:
		draftResult, err := s.handleAutoResponse(ctx, email, triageResult)
		if err != nil {
			s.log.WithContext(ctx).Errorf("Auto-response failed: %v", err)
		} else {
			result.DraftResult = draftResult
		}

	case data.TriageActionNotify:
		err = s.handleNotification(ctx, email, triageResult)
		if err != nil {
			s.log.WithContext(ctx).Errorf("Notification failed: %v", err)
		}

	case data.TriageActionEscalate:
		err = s.handleEscalation(ctx, email, triageResult)
		if err != nil {
			s.log.WithContext(ctx).Errorf("Escalation failed: %v", err)
		}

	case data.TriageActionIgnore:
		s.log.WithContext(ctx).Infof("Email marked for ignore, no action taken")
	}

	// 7. Execute workflow automation (if enabled and not skipped)
	if s.config.EnableWorkflowAutomation && !req.SkipWorkflows {
		workflowResults, err := s.workflowEngine.ExecuteWorkflows(ctx, email, triageResult)
		if err != nil {
			s.log.WithContext(ctx).Warnf("Workflow execution failed: %v", err)
		} else {
			result.WorkflowResults = workflowResults
		}
	}

	// 8. Handle calendar-related requests
	if s.isCalendarRelated(triageResult) {
		calendarResults, err := s.calendarService.ProcessCalendarRequest(ctx, email, triageResult)
		if err != nil {
			s.log.WithContext(ctx).Warnf("Calendar processing failed: %v", err)
		} else {
			result.CalendarEvents = calendarResults
		}
	}

	// 9. Collect performance metrics
	if s.config.EnableMetricsCollection {
		metrics := s.collectProcessingMetrics(ctx, startTime, result)
		result.Metrics = metrics

		err = s.metricsCollector.RecordProcessingMetrics(ctx, metrics)
		if err != nil {
			s.log.WithContext(ctx).Warnf("Failed to record metrics: %v", err)
		}
	}

	result.ProcessingTime = time.Since(startTime)
	result.Success = true

	s.log.WithContext(ctx).Infof("✅ Email processing completed in %v", result.ProcessingTime)
	return result, nil
}

// 🎯 GetTriageHistory - Retrieve triage history for analysis
func (s *Service) GetTriageHistory(ctx context.Context, limit int, filters map[string]interface{}) ([]*data.EmailTriage, error) {
	var triages []*data.EmailTriage

	query := s.db.WithContext(ctx).Model(&data.EmailTriage{}).
		Preload("Email").
		Order("created_at DESC")

	// Apply filters
	if emailCategory, ok := filters["email_category"]; ok {
		query = query.Where("email_category = ?", emailCategory)
	}
	if priorityLevel, ok := filters["priority_level"]; ok {
		query = query.Where("priority_level = ?", priorityLevel)
	}
	if triageAction, ok := filters["triage_action"]; ok {
		query = query.Where("triage_action = ?", triageAction)
	}
	if fromDate, ok := filters["from_date"]; ok {
		query = query.Where("created_at >= ?", fromDate)
	}
	if toDate, ok := filters["to_date"]; ok {
		query = query.Where("created_at <= ?", toDate)
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	err := query.Find(&triages).Error
	if err != nil {
		return nil, fmt.Errorf("failed to retrieve triage history: %w", err)
	}

	return triages, nil
}

// 📝 GetResponseDrafts - Retrieve response drafts for review
func (s *Service) GetResponseDrafts(ctx context.Context, status data.DraftStatus, limit int) ([]*data.EmailResponseDraft, error) {
	var drafts []*data.EmailResponseDraft

	query := s.db.WithContext(ctx).Model(&data.EmailResponseDraft{}).
		Preload("Email").
		Preload("Triage").
		Where("status = ?", status).
		Order("created_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	err := query.Find(&drafts).Error
	if err != nil {
		return nil, fmt.Errorf("failed to retrieve response drafts: %w", err)
	}

	return drafts, nil
}

// ✅ ApproveResponseDraft - Approve and send response draft
func (s *Service) ApproveResponseDraft(ctx context.Context, draftID int64, approvedBy string) error {
	s.log.WithContext(ctx).Infof("📝 Approving response draft ID: %d", draftID)

	// Retrieve draft
	var draft data.EmailResponseDraft
	err := s.db.WithContext(ctx).Preload("Email").First(&draft, draftID).Error
	if err != nil {
		return fmt.Errorf("failed to retrieve draft: %w", err)
	}

	// Update draft status
	now := time.Now()
	err = s.db.WithContext(ctx).Model(&draft).Updates(map[string]interface{}{
		"status":      data.DraftStatusApproved,
		"approved_by": approvedBy,
		"approved_at": &now,
	}).Error
	if err != nil {
		return fmt.Errorf("failed to update draft status: %w", err)
	}

	// Send email (integrate with email service)
	err = s.sendApprovedDraft(ctx, &draft)
	if err != nil {
		// Update status to failed
		s.db.WithContext(ctx).Model(&draft).Update("status", data.DraftStatusFailed)
		return fmt.Errorf("failed to send approved draft: %w", err)
	}

	// Update status to sent
	sentTime := time.Now()
	err = s.db.WithContext(ctx).Model(&draft).Updates(map[string]interface{}{
		"status":  data.DraftStatusSent,
		"sent_at": &sentTime,
	}).Error
	if err != nil {
		s.log.WithContext(ctx).Warnf("Failed to update sent status: %v", err)
	}

	s.log.WithContext(ctx).Infof("✅ Response draft approved and sent successfully")
	return nil
}

// ❌ RejectResponseDraft - Reject response draft with reason
func (s *Service) RejectResponseDraft(ctx context.Context, draftID int64, rejectedBy string, reason string) error {
	s.log.WithContext(ctx).Infof("❌ Rejecting response draft ID: %d", draftID)

	err := s.db.WithContext(ctx).Model(&data.EmailResponseDraft{}).
		Where("id = ?", draftID).
		Updates(map[string]interface{}{
			"status":           data.DraftStatusRejected,
			"approved_by":      rejectedBy,
			"rejection_reason": reason,
		}).Error

	if err != nil {
		return fmt.Errorf("failed to reject draft: %w", err)
	}

	s.log.WithContext(ctx).Infof("✅ Response draft rejected successfully")
	return nil
}

// 🧠 SearchMemoryBank - Search memory bank for relevant information
func (s *Service) SearchMemoryBank(ctx context.Context, query string, entityType string, limit int) ([]*data.ExecutiveMemoryBank, error) {
	return s.memoryBank.SearchMemories(ctx, query, entityType, limit)
}

// 📊 GetPerformanceMetrics - Retrieve performance analytics
func (s *Service) GetPerformanceMetrics(ctx context.Context, category string, fromDate, toDate time.Time) ([]*data.ExecutiveAIMetrics, error) {
	return s.metricsCollector.GetMetrics(ctx, category, fromDate, toDate)
}

// 🔄 ExecuteWorkflowRule - Manually execute a specific workflow rule
func (s *Service) ExecuteWorkflowRule(ctx context.Context, ruleID int64, emailID int64) (*WorkflowExecutionResult, error) {
	return s.workflowEngine.ExecuteSpecificRule(ctx, ruleID, emailID)
}

// 📅 ScheduleMeeting - Schedule a meeting based on email content
func (s *Service) ScheduleMeeting(ctx context.Context, emailID int64, meetingRequest *MeetingRequest) (*CalendarEventResult, error) {
	return s.calendarService.ScheduleMeeting(ctx, emailID, meetingRequest)
}

// ==========================================
// PRIVATE HELPER METHODS
// ==========================================

func (s *Service) getEmailByID(ctx context.Context, emailID int64) (*data.Email, error) {
	var email data.Email
	err := s.db.WithContext(ctx).
		Preload("Analysis").
		Preload("Attachments").
		First(&email, emailID).Error
	if err != nil {
		return nil, fmt.Errorf("email not found: %w", err)
	}
	return &email, nil
}

func (s *Service) getExistingTriage(ctx context.Context, emailID int64) (*data.EmailTriage, error) {
	var triage data.EmailTriage
	err := s.db.WithContext(ctx).Where("email_id = ?", emailID).First(&triage).Error
	if err != nil {
		return nil, err
	}
	return &triage, nil
}

func (s *Service) storeTriageResult(ctx context.Context, emailID int64, result *TriageResult) error {
	triage := &data.EmailTriage{
		EmailID:          emailID,
		TriageAction:     result.Action,
		TriageReason:     result.Reasoning,
		ConfidenceScore:  result.Confidence,
		PriorityLevel:    result.Priority,
		UrgencyScore:     &result.UrgencyScore,
		EmailCategory:    result.Category,
		Subcategory:      &result.Subcategory,
		HVACServiceType:  &result.HVACServiceType,
		EquipmentType:    &result.EquipmentType,
		CustomerType:     &result.CustomerType,
		DetectedIntent:   &result.DetectedIntent,
		DetectedEntities: result.DetectedEntities,
		KeyPhrases:       result.KeyPhrases,
		Sentiment:        &result.Sentiment,
		SentimentScore:   &result.SentimentScore,
		AIModelUsed:      result.AIModelUsed,
		ProcessingTimeMs: &result.ProcessingTimeMs,
	}

	err := s.db.WithContext(ctx).Create(triage).Error
	if err != nil {
		return fmt.Errorf("failed to store triage result: %w", err)
	}

	return nil
}

func (s *Service) handleAutoResponse(ctx context.Context, email *data.Email, triage *TriageResult) (*DraftResult, error) {
	s.log.WithContext(ctx).Info("🤖 Generating auto-response")

	return s.draftEngine.GenerateResponse(ctx, email, triage)
}

func (s *Service) handleNotification(ctx context.Context, email *data.Email, triage *TriageResult) error {
	s.log.WithContext(ctx).Info("🔔 Sending notification")

	// Implement notification logic (email, Slack, etc.)
	// This would integrate with your notification system
	return nil
}

func (s *Service) handleEscalation(ctx context.Context, email *data.Email, triage *TriageResult) error {
	s.log.WithContext(ctx).Info("🚨 Escalating email")

	// Implement escalation logic
	// This would route to appropriate team/person
	return nil
}

func (s *Service) isCalendarRelated(triage *TriageResult) bool {
	calendarKeywords := []string{"schedule", "meeting", "appointment", "calendar", "book", "available"}

	for _, keyword := range calendarKeywords {
		if strings.Contains(strings.ToLower(triage.DetectedIntent), keyword) {
			return true
		}
	}

	return false
}

func (s *Service) sendApprovedDraft(ctx context.Context, draft *data.EmailResponseDraft) error {
	// This would integrate with your email sending service
	// For now, we'll just log it
	s.log.WithContext(ctx).Infof("📧 Sending email: %s", draft.DraftSubject)

	// In a real implementation, you would:
	// 1. Format the email properly
	// 2. Send via SMTP or email service
	// 3. Handle delivery status
	// 4. Update the draft with sent message ID

	return nil
}

func (s *Service) convertTriageToResult(triage *data.EmailTriage) *TriageResult {
	result := &TriageResult{
		Action:           triage.TriageAction,
		Reasoning:        triage.TriageReason,
		Confidence:       triage.ConfidenceScore,
		Priority:         triage.PriorityLevel,
		Category:         triage.EmailCategory,
		DetectedEntities: triage.DetectedEntities,
		KeyPhrases:       triage.KeyPhrases,
		AIModelUsed:      triage.AIModelUsed,
		ProcessingTimeMs: *triage.ProcessingTimeMs,
	}

	if triage.UrgencyScore != nil {
		result.UrgencyScore = *triage.UrgencyScore
	}
	if triage.Subcategory != nil {
		result.Subcategory = *triage.Subcategory
	}
	if triage.HVACServiceType != nil {
		result.HVACServiceType = *triage.HVACServiceType
	}
	if triage.EquipmentType != nil {
		result.EquipmentType = *triage.EquipmentType
	}
	if triage.CustomerType != nil {
		result.CustomerType = *triage.CustomerType
	}
	if triage.DetectedIntent != nil {
		result.DetectedIntent = *triage.DetectedIntent
	}
	if triage.Sentiment != nil {
		result.Sentiment = *triage.Sentiment
	}
	if triage.SentimentScore != nil {
		result.SentimentScore = *triage.SentimentScore
	}

	return result
}

func (s *Service) collectProcessingMetrics(ctx context.Context, startTime time.Time, result *EmailProcessingResult) *ProcessingMetrics {
	return &ProcessingMetrics{
		ProcessingTime:    result.ProcessingTime,
		TriageConfidence:  result.TriageResult.Confidence,
		WorkflowsExecuted: len(result.WorkflowResults),
		MemoriesCreated:   len(result.MemoryUpdates),
		CalendarEvents:    len(result.CalendarEvents),
		Success:           result.Success,
	}
}